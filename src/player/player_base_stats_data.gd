class_name PlayerBaseStatsData
extends Resource

@export_group("Core Stats")
@export var max_health: int = 1
@export var max_paint: int = 10
@export var move_duration: float = 0.15
@export var extra_lives: int = 0
@export var size_modifier: float = 1.0

@export_group("Movement")
@export var tile_size: int = 8

@export_group("Input")
@export var initial_repeat_delay: float = 0.15
@export var repeat_rate: float = 0.1

@export_group("Other Properties")
@export var paint_color: Color = Color.WHITE
