extends Node

const HEALTH_PER_UPGRADE = 1
const PAINT_PER_UPGRADE = 2
const MOVE_DURATION_REDUCTION_PER_UPGRADE = 0.01
const EXTRA_LIVES_PER_UPGRADE = 1
const SIZE_MODIFIER_PER_UPGRADE = 0.05

var _effective_max_health: int
var _effective_max_paint: int
var _effective_move_duration: float
var _effective_extra_lives: int
var _effective_size_modifier: float
var _paint_color: Color
var _tile_size: int
var _initial_repeat_delay: float
var _repeat_rate: float

func initialize_for_run(player_stats_component: StatsComponent) -> void:
	var base := player_stats_component.base_stats

	_effective_max_health = base.max_health + (GameProgress.get_stat_upgrade_level("max_health") * HEALTH_PER_UPGRADE)
	_effective_max_paint = base.max_paint + (GameProgress.get_stat_upgrade_level("max_paint") * PAINT_PER_UPGRADE)
	_effective_move_duration = base.move_duration - (GameProgress.get_stat_upgrade_level("move_duration") * MOVE_DURATION_REDUCTION_PER_UPGRADE)
	_effective_extra_lives = base.extra_lives + (GameProgress.get_stat_upgrade_level("extra_lives") * EXTRA_LIVES_PER_UPGRADE)
	_effective_size_modifier = base.size_modifier + (GameProgress.get_stat_upgrade_level("size_modifier") * SIZE_MODIFIER_PER_UPGRADE)

	_paint_color = base.paint_color
	_tile_size = base.tile_size
	_initial_repeat_delay = base.initial_repeat_delay
	_repeat_rate = base.repeat_rate

func spend_extra_life() -> bool:
	if _effective_extra_lives > 0:
		_effective_extra_lives -= 1
		return true
	return false

func get_max_health() -> int:
	return _effective_max_health

func get_max_paint() -> int:
	return _effective_max_paint

func get_move_duration() -> float:
	return max(0.05, _effective_move_duration)

func get_extra_lives() -> int:
	return _effective_extra_lives

func get_size_modifier() -> float:
	return _effective_size_modifier

func get_paint_color() -> Color:
	return _paint_color

func get_tile_size() -> int:
	return _tile_size

func get_initial_repeat_delay() -> float:
	return _initial_repeat_delay

func get_repeat_rate() -> float:
	return _repeat_rate
