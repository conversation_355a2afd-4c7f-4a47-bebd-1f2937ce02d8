[gd_resource type="Resource" script_class="UpgradeableStatData" load_steps=2 format=3 uid="uid://ca3o6b4j0tx5"]

[ext_resource type="Script" path="res://src/stats/upgradeable_stat_data.gd" id="1_extra_lives"]

[resource]
script = ExtResource("1_extra_lives")
stat_id = &"extra_lives"
display_name = "Доп. жизни"
description = "Добавляет 1 дополнительную жизнь в забеге"
base_cost = 25
cost_increase_per_level = 15
